'use client';

import { useEffect } from 'react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Error caught:', error);
  }, [error]);

  return (
    <div>
      <h1>Something went wrong</h1>
      <p>We apologize for the inconvenience. An unexpected error occurred.</p>
      <button onClick={reset}>Try again</button>
      <a href="/">Go Home</a>
    </div>
  );
}
