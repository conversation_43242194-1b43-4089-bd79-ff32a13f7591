import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import SessionWrapper from '@/components/SessionWrapper';
import CookieConsentBanner from "@/components/CookieConsentBanner";
import { ThemeProvider } from "@/components/layout/ThemeProvider";
import VercelAnalyticsWrapper from "@/app/components/layout/VercelAnalyticsWrapper";
import Footer from "@/components/layout/Footer";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { NavigationBar } from "@/components/layout/NavigationBar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Viewport configuration moved to metadata for compatibility

export const metadata: Metadata = {
  title: "FAAFO Career Platform - Find Your Path to Career Freedom",
  description: "Empowering career transitions through personalized assessments, financial planning, and community support. Take control of your career journey with FAAFO.",
  keywords: "career, freedom, assessment, learning, professional development, career transition",
  authors: [{ name: "FAAFO Team" }],
  robots: "index, follow",
  openGraph: {
    title: "FAAFO Career Platform - Find Your Path to Career Freedom",
    description: "Empowering career transitions through personalized assessments, financial planning, and community support.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "FAAFO Career Platform",
    description: "Find your path to career freedom",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#3b82f6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground min-h-screen flex flex-col`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          <SessionWrapper>
            <main id="main-content" className="flex-grow min-h-0">
              {children}
            </main>
          </SessionWrapper>
        </ThemeProvider>
      </body>
    </html>
  );
}
